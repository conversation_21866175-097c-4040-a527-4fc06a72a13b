import OpenAI from "openai";
import { OpenAIEmbeddings } from "@langchain/openai"
import {
    readFileSync,
    writeFileSync,
    existsSync,
    mkdirSync
} from "fs";

import { sha256 } from "js-sha256";
import { ChatCompletionCreateParamsNonStreaming, ChatCompletionMessageToolCall } from "openai/resources";
import { HttpsProxyAgent } from 'https-proxy-agent';

// Configure proxy if environment variables are set
const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY || process.env.http_proxy || process.env.HTTP_PROXY;
const httpAgent = proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined;

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,

    httpAgent: httpAgent,
});

type OpenAIContext = { result: string, ctx: any, function_calls?: ChatCompletionMessageToolCall[]};
type Vulnerability = { vulnerableFunction: string, vulnerabilityReason: string, vulnerabilityType: string, vulnerabilityAssertion?: string, vulnerabilityLikelihood?: string };

async function openaiCompletion(prompt: string, nth = 0, ctx = [], role = "user", functions=[]): Promise<OpenAIContext> {
    if (!existsSync(".cache/")) mkdirSync(".cache/");
    let hash = sha256(prompt + JSON.stringify(ctx));
    let newCtx = [
        ...ctx,
        {
            "role": role,
            "content": prompt
        },
    ];
    if (existsSync(`.cache/${hash}-${nth}-response`)) {
        let result = readFileSync(`.cache/${hash}-${nth}-response`, "utf-8");
        var openAIContext: OpenAIContext = {
            result,
            ctx: newCtx
        }
        // TODO: load tool call info here
        if (existsSync(`.cache/${hash}-${nth}-tools`)) {
            openAIContext.function_calls = JSON.parse(readFileSync(`.cache/${hash}-${nth}-tools`, "utf-8"));
        }
        newCtx.push({
            "role": "assistant",
            "content": result,
        });

        return openAIContext;
    }

    var apiParams: ChatCompletionCreateParamsNonStreaming = {
        model: "gpt-4-turbo-preview",
        messages: [
            ...ctx,
            {
                "role": "user",
                "content": prompt
            },
        ],
        temperature: 1,
        max_tokens: 1500,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
    };

    if (functions?.length) {
        apiParams.tools = functions;
    }

    console.log("Debug: Making OpenAI API call with params:", JSON.stringify(apiParams, null, 2));
    const response = await openai.chat.completions.create(apiParams);
    console.log("Debug: OpenAI response:", JSON.stringify(response, null, 2));
    let result = response.choices[0].message.content || '';
    let function_calls: ChatCompletionMessageToolCall[] = response.choices[0].message.tool_calls || [];
    console.log("Debug: Extracted result:", result);
    console.log("Debug: Extracted function_calls:", function_calls);
    writeFileSync(`.cache/${hash}-${nth}-response`, response.choices[0].message.content || '', {
        encoding: "utf-8",
        flag: "w+"
    });
    writeFileSync(`.cache/${hash}-${nth}-tools`, JSON.stringify(function_calls) || '', {
        encoding: "utf-8",
        flag: "w+"
    });
    newCtx.push({
        "role": "assistant",
        "content": result,
    });
    return {
        result,
        ctx: newCtx,
        function_calls: function_calls
    };
}

function vulnerabilityTransformer(vulnerabilityCtx: OpenAIContext): Vulnerability {
    console.log("Debug: vulnerabilityCtx.function_calls:", vulnerabilityCtx.function_calls);
    if (!vulnerabilityCtx.function_calls || vulnerabilityCtx.function_calls.length === 0) {
        console.log("Debug: No function calls found. Full context:", JSON.stringify(vulnerabilityCtx, null, 2));
        throw new Error("No function calls found in vulnerability context");
    }
    console.log("Debug: First function call structure:", JSON.stringify(vulnerabilityCtx.function_calls[0], null, 2));

    // Check the actual structure of the function call
    const functionCall = vulnerabilityCtx.function_calls[0];
    console.log("Debug: functionCall keys:", Object.keys(functionCall));
    console.log("Debug: functionCall.function:", functionCall.function);

    if (!functionCall.function) {
        throw new Error("Function call does not have 'function' property. Structure: " + JSON.stringify(functionCall, null, 2));
    }

    let vulnerabilityInfo = JSON.parse(functionCall.function.arguments);
    return {
        vulnerableFunction: vulnerabilityInfo["vulnerableFunctionName"],
        vulnerabilityReason: vulnerabilityInfo["vulnerabilityDescription"],
        vulnerabilityType: vulnerabilityInfo["vulnerabilityType"]
    };
}

function vulnerabilityEvaluationTransformer(vulnerability: Vulnerability, vulnerabilityCtx: OpenAIContext): Vulnerability {
    let vulnerabilityEvaluationInfo = JSON.parse(vulnerabilityCtx.function_calls[0].function.arguments);
    return {
        vulnerableFunction: vulnerability.vulnerableFunction,
        vulnerabilityReason: vulnerability.vulnerabilityReason,
        vulnerabilityType: vulnerability.vulnerabilityType,
        vulnerabilityAssertion: vulnerabilityEvaluationInfo["vulnerabilityAssertion"],
        vulnerabilityLikelihood: vulnerabilityEvaluationInfo["vulnerabilityLikelihood"]
    }
}


async function solidityTransformer(input: string, nth = 0, ctx = []): Promise<{ code: string[], ctx: any }> {
    let { result, ctx: newCtx } = await openaiCompletion(input, nth, ctx);
    if (result === null) return {
        code: [],
        ctx: newCtx
    };
    let insideSolidityCode = false;
    let lines = result.split("\n");
    let newLines = [];
    let newComments = [];

    let transformedCode = []

    if (!result.includes("```solidity")) {
        return {
            code: [result.replace(/```/g, "").replace(/\d+\. function/g, "function")],
            ctx: newCtx
        }
    }

    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes("```solidity")) {
            insideSolidityCode = true;
        } else if (lines[i].includes("```")) {
            insideSolidityCode = false;
            let joinedCode = newComments.join("\n") + "\n" + newLines.join("\n");
            transformedCode.push(joinedCode.replace(/\d+\. function/g, "function"));
        } else if (insideSolidityCode) {
            newLines.push(lines[i]);
        } else if (!insideSolidityCode) {
            newComments.push("// " + lines[i]);
        }
    }
    return { code: transformedCode || [result], ctx: newCtx }
}

function getOpenAIEmbedding() {
    return new OpenAIEmbeddings({apiKey: process.env.OPENAI_API_KEY});
}


export { solidityTransformer, getOpenAIEmbedding, openaiCompletion, OpenAIContext, vulnerabilityTransformer, vulnerabilityEvaluationTransformer, Vulnerability};