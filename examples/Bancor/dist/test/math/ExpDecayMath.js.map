{"version": 3, "file": "ExpDecayMath.js", "sourceRoot": "", "sources": ["../../../test/math/ExpDecayMath.ts"], "names": [], "mappings": ";;;;;AAAA,2EAAyE;AACzE,6CAA0C;AAC1C,0CAA2C;AAC3C,0CAAuC;AACvC,+BAA8B;AAC9B,4DAAiC;AAGjC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,eAAQ,CAAC;AAE1D,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC1B,IAAI,YAA8B,CAAC;IACnC,MAAM,GAAG,GAAG,IAAI,oBAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,GAAG,GAAG,IAAI,oBAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,GAAG,GAAG,IAAI,oBAAO,CAAC,GAAG,CAAC,CAAC;IAE7B,MAAM,CAAC,KAAK,IAAI,EAAE;QACd,YAAY,GAAG,MAAM,mBAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,CAAC,SAAuB,EAAE,WAAmB,EAAE,QAAgB,EAAE,EAAE;QACpF,EAAE,CAAC,gBAAgB,SAAS,KAAK,WAAW,KAAK,QAAQ,GAAG,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,CAAC,GAAG,IAAI,oBAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;gBACZ,MAAM,CAAC,GAAG,IAAI,oBAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACjD,uCAAuC;gBACvC,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACjF,kDAAkD;gBAClD,MAAM,QAAQ,GAAG,IAAI,oBAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5E,IAAA,aAAM,EAAC,MAAM,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE;oBACpC,gBAAgB,EAAE,IAAI,oBAAO,CAAC,CAAC,CAAC;oBAChC,QAAQ,EAAE,mBAAQ,CAAC,aAAa;iBACnC,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAA,aAAM,EAAC,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAC1F,iBAAiB,CACpB,CAAC;YACN,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,qEAAqE;QACrE,EAAE,CAAC,gBAAgB,SAAS,KAAK,QAAQ,KAAK,QAAQ,GAAG,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC9E,MAAM,QAAQ,GAAG,IAAI,oBAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5D,IAAA,aAAM,EAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC3B,KAAK,MAAM,SAAS,IAAI,CAAC,UAAU,EAAE,IAAA,aAAK,EAAC,CAAC,CAAC,EAAE,IAAA,aAAK,EAAC,UAAU,CAAC,CAAC,EAAE,CAAC;YAChE,KAAK,MAAM,WAAW,IAAI;gBACtB,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC;gBACV,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,CAAC,CAAC,CAAC;gBACV,OAAO,CAAC,EAAE,CAAC;gBACX,KAAK,CAAC,CAAC,CAAC;gBACR,KAAK,CAAC,EAAE,CAAC;gBACT,IAAI,CAAC,CAAC,CAAC;gBACP,IAAI,CAAC,CAAC,CAAC;gBACP,IAAI,CAAC,CAAC,CAAC;gBACP,IAAI,CAAC,EAAE,CAAC;gBACR,IAAI,CAAC,GAAG,CAAC;gBACT,KAAK,CAAC,CAAC,CAAC;gBACR,KAAK,CAAC,CAAC,CAAC;aACX,EAAE,CAAC;gBACA,KAAK,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAC5D,YAAY,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACnD,CAAC;YACL,CAAC;QACL,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3E,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,cAAc;YAChF,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,cAAc;QACpF,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC3B,KAAK,MAAM,SAAS,IAAI;YACpB,UAAU;YACV,WAAW;YACX,aAAa;YACb,IAAA,aAAK,EAAC,UAAU,CAAC;YACjB,IAAA,aAAK,EAAC,WAAW,CAAC;YAClB,IAAA,aAAK,EAAC,aAAa,CAAC;SACvB,EAAE,CAAC;YACA,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE,CAAC;gBACpD,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE,CAAC;oBACpD,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC;wBAC9C,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;4BAC3C,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC;gCAC9C,KAAK,MAAM,QAAQ,IAAI;oCACnB,IAAI,CAAC,CAAC,CAAC;oCACP,IAAI,CAAC,EAAE,CAAC;oCACR,KAAK,CAAC,GAAG,CAAC;oCACV,KAAK,CAAC,CAAC,CAAC;oCACR,KAAK,CAAC,GAAG,CAAC;oCACV,KAAK,CAAC,CAAC,CAAC;iCACX,EAAE,CAAC;oCACA,YAAY,CACR,SAAS,EACT,OAAO,CAAC,UAAU,CAAC;wCACf,OAAO,CAAC,UAAU,CAAC;wCACnB,KAAK,CAAC,QAAQ,CAAC;wCACf,IAAI,CAAC,OAAO,CAAC;wCACb,KAAK,CAAC,QAAQ,CAAC,EACnB,QAAQ,CACX,CAAC;gCACN,CAAC;4BACL,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}