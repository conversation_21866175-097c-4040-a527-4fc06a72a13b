{"version": 3, "file": "Upgradeable.js", "sourceRoot": "", "sources": ["../../../test/utility/Upgradeable.ts"], "names": [], "mappings": ";;;;;AAAA,2EAAwE;AACxE,4DAA0E;AAC1E,4CAAiD;AAEjD,+BAA8B;AAC9B,qCAAiC;AAEjC,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IACzB,IAAI,KAAwB,CAAC;IAC7B,IAAI,QAA2B,CAAC;IAEhC,IAAI,WAA4B,CAAC;IAEjC,IAAA,qBAAa,EAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;IAEjD,MAAM,CAAC,KAAK,IAAI,EAAE;QACd,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,MAAM,gBAAM,CAAC,UAAU,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QAClB,WAAW,GAAG,MAAM,mBAAS,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAEvD,MAAM,WAAW,CAAC,UAAU,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,IAAA,aAAM,EAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,CAC1D,gDAAgD,CACnD,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC5C,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChD,IAAA,aAAM,EAAC,MAAM,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAExD,MAAM,IAAA,2BAAW,EAAC,WAAW,EAAE,qBAAK,CAAC,WAAW,CAAC,CAAC;YAElD,MAAM,IAAA,0BAAU,EAAC,WAAW,EAAE,qBAAK,CAAC,WAAW,CAAC,UAAU,EAAE,qBAAK,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4EAA4E,EAAE,KAAK,IAAI,EAAE;YACxF,MAAM,IAAA,aAAM,EAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC/B,OAAO,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAChC,UAAU,CAAC,KAAK,IAAI,EAAE;gBAClB,MAAM,WAAW,CAAC,UAAU,CAAC,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;gBAC9D,MAAM,IAAA,aAAM,EAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;gBAE7D,MAAM,WAAW,CAAC,UAAU,CAAC,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;gBAEhE,MAAM,IAAA,aAAM,EAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;YACjE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;gBACpF,MAAM,IAAA,aAAM,EAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;gBAC7D,MAAM,IAAA,aAAM,EAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YAC5F,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,eAAe,EAAE,GAAG,EAAE;YAC1B,KAAK,MAAM,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;gBACzB,OAAO,CAAC,QAAQ,IAAI,EAAE,EAAE,GAAG,EAAE;oBACzB,UAAU,CAAC,KAAK,IAAI,EAAE;wBAClB,MAAM,WAAW,CAAC,UAAU,CAAC,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;oBACvE,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,oEAAoE,EAAE,KAAK,IAAI,EAAE;oBAChF,MAAM,IAAA,aAAM,EAAC,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;gBAC5F,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}