{"version": 3, "file": "OnlyProxyDelegate.js", "sourceRoot": "", "sources": ["../../../test/utility/OnlyProxyDelegate.ts"], "names": [], "mappings": ";;;;;AAAA,2EAAmD;AACnD,qDAAqD;AACrD,qDAA6D;AAC7D,+BAA8B;AAE9B,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IAC/B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,qBAAqB,GAAG,MAAM,mBAAS,CAAC,qBAAqB,CAAC,MAAM,CAAC,wBAAY,CAAC,CAAC;QACzF,MAAM,EAAE,GAAG,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACzD,MAAM,IAAA,aAAM,EAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;QACzE,MAAM,qBAAqB,GAAG,MAAM,mBAAS,CAAC,qBAAqB,CAAC,MAAM,CAAC,gCAAoB,CAAC,CAAC;QACjG,MAAM,EAAE,GAAG,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACzD,MAAM,IAAA,aAAM,EAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}