{"version": 3, "file": "BigNumber.js", "sourceRoot": "", "sources": ["../../../test/matchers/BigNumber.ts"], "names": [], "mappings": ";;;;;AAAA,6CAAgD;AAChD,0CAAuC;AACvC,+BAA8B;AAC9B,4DAAiC;AACjC,mCAAmC;AAEnC,MAAM,gBAAgB,GAAG,CAAC,SAA+B,EAAE,KAAqB,EAAE,EAAE;IAChF,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;IAC9D,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7D,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;IAC1D,SAAS,CAAC,eAAe,CAAC,aAAa,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,KAAqB,EAAE,EAAE;IACrD,OAAO,CAAC,MAA+B,EAAE,EAAE,CAAC,0BAA0B,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAChG,CAAC,CAAC;AAEF,MAAM,0BAA0B,GAAG,CAC/B,YAAoB,EACpB,MAA+B,EAC/B,SAAyB,EAC3B,EAAE;IACA,OAAO,UAAsC,GAAG,IAAW;QACvD,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;QACxB,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAE9C,IAAI,kBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,kBAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnE,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAAY,MAAM,CAAC,CAAC;YAChD,MAAM,UAAU,GAAG,IAAA,mBAAW,EAAY,QAAQ,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,CACP,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,EACvB,YAAY,QAAQ,UAAU,YAAY,IAAI,UAAU,EAAE,EAC1D,YAAY,QAAQ,cAAc,YAAY,IAAI,UAAU,EAAE,EAC9D,UAAU,CAAC,QAAQ,EAAE,EACrB,QAAQ,CAAC,QAAQ,EAAE,CACtB,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,KAAqB,EAAE,EAAE;IAClD,OAAO,CAAC,MAAmC,EAAE,EAAE,CAAC,6BAA6B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACjG,CAAC,CAAC;AAEF,MAAM,6BAA6B,GAAG,CAAC,MAA+B,EAAE,SAAyB,EAAE,EAAE;IACjG,OAAO,UAAsC,GAAG,IAAW;QACvD,MAAM,CACF,QAAQ,EACR,EAAE,gBAAgB,GAAG,IAAI,oBAAO,CAAC,CAAC,CAAC,EAAE,gBAAgB,GAAG,IAAI,oBAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,SAAS,EAAE,CACjG,GAAG,IAAI,CAAC;QACT,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAE9C,IAAA,aAAM,EAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,oBAAO,CAAC,CAAC;QACnD,IAAA,aAAM,EAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,oBAAO,CAAC,CAAC;QAEnD,IAAI,kBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,kBAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,oBAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjD,MAAM,WAAW,GAAG,IAAI,oBAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YAErD,IAAI,SAAS,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5B,OAAO;YACX,CAAC;YAED,QAAQ,QAAQ,EAAE,CAAC;gBACf,KAAK,mBAAQ,CAAC,aAAa;oBACvB,IAAI,CAAC,MAAM,CACP,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAC1B,YAAY,SAAS,kCAAkC,WAAW,EAAE,EACpE,YAAY,SAAS,sCAAsC,WAAW,EAAE,EACxE,WAAW,CAAC,QAAQ,EAAE,EACtB,SAAS,CAAC,QAAQ,EAAE,CACvB,CAAC;oBACF,MAAM;gBACV,KAAK,mBAAQ,CAAC,cAAc;oBACxB,IAAI,CAAC,MAAM,CACP,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAC1B,YAAY,SAAS,mCAAmC,WAAW,EAAE,EACrE,YAAY,SAAS,uCAAuC,WAAW,EAAE,EACzE,WAAW,CAAC,QAAQ,EAAE,EACtB,SAAS,CAAC,QAAQ,EAAE,CACvB,CAAC;oBACF,MAAM;YACd,CAAC;YAED,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;YACvD,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CACP,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAC1E,YAAY,SAAS,CAAC,OAAO,EAAE,0BAA0B,WAAW,CAAC,OAAO,EAAE;qCACzD,aAAa,CAAC,OAAO,EAAE;qCACvB,aAAa,CAAC,OAAO,EAAE,EAAE,EAC9C,YAAY,SAAS,CAAC,OAAO,EAAE,8BAA8B,WAAW,CAAC,OAAO,EAAE;qCAC7D,aAAa,CAAC,OAAO,EAAE;qCACvB,aAAa,CAAC,OAAO,EAAE,EAAE,EAC9C,WAAW,CAAC,OAAO,EAAE,EACrB,SAAS,CAAC,OAAO,EAAE,CACtB,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,kBAAe,gBAAgB,CAAC"}