{"version": 3, "file": "AccessControl.js", "sourceRoot": "", "sources": ["../../../test/helpers/AccessControl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA,+BAA8B;AAC9B,mCAA+B;AAC/B,mCAAmC;AAEnC,MAAM,EAAE,EAAE,EAAE,GAAG,cAAK,CAAC;AAErB,oDAAkC;AAE3B,MAAM,UAAU,GAAG,KAAK,EAC3B,QAA4C,EAC5C,MAAgC,EAChC,SAAiB,EACjB,UAAoB,EAAE,EACxB,EAAE;IACA,IAAA,aAAM,EAAC,MAAM,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAEhE,MAAM,IAAA,yBAAiB,EAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACvD,CAAC,CAAC;AATW,QAAA,UAAU,cASrB;AAEK,MAAM,iBAAiB,GAAG,KAAK,EAClC,QAA4C,EAC5C,MAAgC,EAChC,UAAoB,EAAE,EACxB,EAAE;IACA,MAAM,aAAa,GAAG,EAAE,CAAC;IACzB,MAAM,WAAW,GAAG,CAAC,MAAM,QAAQ,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAE3E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,aAAa,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,IAAA,aAAM,EAAC,aAAa,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvD,IAAA,aAAM,EAAC,aAAa,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACnD,CAAC,CAAC;AAdW,QAAA,iBAAiB,qBAc5B;AAEK,MAAM,WAAW,GAAG,KAAK,EAC5B,QAA4C,EAC5C,KAA+C,EACjD,EAAE;IACA,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACpD,UAAU,EAAE,IAAA,kBAAS,EAAC,IAAI,CAAC;QAC3B,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC;KACf,CAAC,CAAC,CAAC;IAEJ,KAAK,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,IAAI,aAAa,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAI,QAAgB,CAAC,UAAU,CAAuB,CAAC;QACnE,IAAA,aAAM,EAAC,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;AACL,CAAC,CAAC;AAbW,QAAA,WAAW,eAatB"}