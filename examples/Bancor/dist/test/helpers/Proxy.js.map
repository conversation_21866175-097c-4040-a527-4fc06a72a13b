{"version": 3, "file": "Proxy.js", "sourceRoot": "", "sources": ["../../../test/helpers/Proxy.ts"], "names": [], "mappings": ";;;AAAA,+BAA8B;AAC9B,qCAAsC;AACtC,mCAAkC;AAElC,MAAM,SAAS,GAAG,OAAO,CAAC;AAC1B,MAAM,YAAY,GAAG,EAAE,CAAC;AACxB,MAAM,eAAe,GAAG,6BAA6B,CAAC;AAEtD,sHAAsH;AACtH,2EAA2E;AACpE,MAAM,aAAa,GAAG,CAAC,YAAoB,EAAE,kBAA2B,EAAE,EAAE;IAC/E,EAAE,CAAC,GAAG,YAAY,qBAAqB,YAAY,wCAAwC,EAAE,KAAK,IAAI,EAAE;QACpG,MAAM,gBAAgB,GAAG,MAAM,qBAAW,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QAC7E,MAAM,EACF,aAAa,EAAE,EAAE,OAAO,EAAE,EAC7B,GAAG,gBAAgB,CAAC;QAErB,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;QACzC,IAAA,aAAM,EAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAE9C,6GAA6G;QAC7G,wBAAwB;QACxB,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnE,IAAI,kBAAkB,EAAE,CAAC;YACrB,8FAA8F;YAC9F,4EAA4E;YAC5E,MAAM,YAAY,GAAG,IAAA,iBAAQ,EAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,kBAAkB,CAAC,CAAC;YACpF,IAAA,aAAM,EAAC,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAClF,CAAC;aAAM,CAAC;YACJ,4GAA4G;YAC5G,2FAA2F;YAC3F,IAAA,aAAM,EAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AA1BW,QAAA,aAAa,iBA0BxB"}