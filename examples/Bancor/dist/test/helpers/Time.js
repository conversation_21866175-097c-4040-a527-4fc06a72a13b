"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.increaseTime = exports.latest = void 0;
const hardhat_1 = require("hardhat");
__exportStar(require("../../utils/Time"), exports);
const latest = async () => {
    const block = await hardhat_1.ethers.provider.getBlock('latest');
    return block.timestamp;
};
exports.latest = latest;
const increaseTime = async (seconds) => hardhat_1.ethers.provider.send('evm_increaseTime', [hardhat_1.ethers.utils.hexValue(seconds)]);
exports.increaseTime = increaseTime;
//# sourceMappingURL=Time.js.map