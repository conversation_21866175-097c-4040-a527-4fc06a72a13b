"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NamedAccounts = void 0;
const Constants_1 = require("../utils/Constants");
const chainIds_json_1 = __importDefault(require("../utils/chainIds.json"));
const { TENDERLY_NETWORK_NAME = Constants_1.DeploymentNetwork.Mainnet } = process.env;
const TENDERLY_NETWORK_ID = chainIds_json_1.default[TENDERLY_NETWORK_NAME];
const mainnet = (address) => {
    if (TENDERLY_NETWORK_ID === chainIds_json_1.default[Constants_1.DeploymentNetwork.Mainnet]) {
        return {
            [Constants_1.DeploymentNetwork.Mainnet]: address,
            [Constants_1.DeploymentNetwork.Tenderly]: address,
            [Constants_1.DeploymentNetwork.TenderlyTestnet]: address
        };
    }
    return {
        [Constants_1.DeploymentNetwork.Mainnet]: address
    };
};
const base = (address) => {
    if (TENDERLY_NETWORK_ID === chainIds_json_1.default[Constants_1.DeploymentNetwork.Base]) {
        return {
            [Constants_1.DeploymentNetwork.Base]: address,
            [Constants_1.DeploymentNetwork.Tenderly]: address,
            [Constants_1.DeploymentNetwork.TenderlyTestnet]: address
        };
    }
    return {
        [Constants_1.DeploymentNetwork.Base]: address
    };
};
const arbitrum = (address) => {
    if (TENDERLY_NETWORK_ID === chainIds_json_1.default[Constants_1.DeploymentNetwork.Arbitrum]) {
        return {
            [Constants_1.DeploymentNetwork.Arbitrum]: address,
            [Constants_1.DeploymentNetwork.Tenderly]: address,
            [Constants_1.DeploymentNetwork.TenderlyTestnet]: address
        };
    }
    return {
        [Constants_1.DeploymentNetwork.Arbitrum]: address
    };
};
const sepolia = (address) => {
    if (TENDERLY_NETWORK_ID === chainIds_json_1.default[Constants_1.DeploymentNetwork.Sepolia]) {
        return {
            [Constants_1.DeploymentNetwork.Sepolia]: address,
            [Constants_1.DeploymentNetwork.Tenderly]: address,
            [Constants_1.DeploymentNetwork.TenderlyTestnet]: address
        };
    }
    return {
        [Constants_1.DeploymentNetwork.Sepolia]: address
    };
};
const mantle = (address) => {
    if (TENDERLY_NETWORK_ID === chainIds_json_1.default[Constants_1.DeploymentNetwork.Mantle]) {
        return {
            [Constants_1.DeploymentNetwork.Mantle]: address,
            [Constants_1.DeploymentNetwork.Tenderly]: address,
            [Constants_1.DeploymentNetwork.TenderlyTestnet]: address
        };
    }
    return {
        [Constants_1.DeploymentNetwork.Mantle]: address
    };
};
const TestNamedAccounts = {
    ethWhale: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, '******************************************'),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, '******************************************')
    },
    daiWhale: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, '******************************************'),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, Constants_1.ZERO_ADDRESS)
    },
    usdcWhale: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(mantle, '******************************************'),
        ...getAddress(base, '******************************************'),
        ...getAddress(arbitrum, '******************************************')
    },
    wbtcWhale: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, '******************************************')
    },
    bntWhale: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(mantle, Constants_1.ZERO_ADDRESS)
    },
    linkWhale: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, Constants_1.ZERO_ADDRESS)
    }
};
const TokenNamedAccounts = {
    dai: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, '******************************************'),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, Constants_1.ZERO_ADDRESS)
    },
    weth: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, '******************************************'),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, '******************************************')
    },
    usdc: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, '******************************************'),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, '******************************************')
    },
    wbtc: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, Constants_1.ZERO_ADDRESS),
        ...getAddress(arbitrum, '******************************************'),
        ...getAddress(mantle, '******************************************')
    },
    bnt: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, Constants_1.ZERO_ADDRESS),
        ...getAddress(arbitrum, Constants_1.ZERO_ADDRESS),
        ...getAddress(mantle, Constants_1.ZERO_ADDRESS)
    },
    link: {
        ...getAddress(mainnet, '******************************************'),
        ...getAddress(base, Constants_1.ZERO_ADDRESS),
        ...getAddress(arbitrum, Constants_1.ZERO_ADDRESS),
        ...getAddress(mantle, Constants_1.ZERO_ADDRESS)
    }
};
const BancorNamedAccounts = {
    bancorNetworkV3: {
        ...getAddress(mainnet, '******************************************')
    },
    vault: {
        ...getAddress(mainnet, Constants_1.ZERO_ADDRESS),
        ...getAddress(base, '0xD2b2D272c30d9a0ff3DbaFe848DA7e2f194f697F')
    },
    oldVortex: {
        ...getAddress(mainnet, '0xba7d1581Db6248DC9177466a328BF457703c8f84')
    }
};
function getAddress(func, arg) {
    const result = func(arg);
    return result || {};
}
exports.NamedAccounts = {
    deployer: {
        ...getAddress(mainnet, 'ledger://0x5bEBA4D3533a963Dedb270a95ae5f7752fA0Fe22'),
        ...getAddress(sepolia, 'ledger://0x0f28D58c00F9373C00811E9576eE803B4eF98abe'),
        ...getAddress(base, 'ledger://0x0f28D58c00F9373C00811E9576eE803B4eF98abe'),
        ...getAddress(arbitrum, 'ledger://0x0f28D58c00F9373C00811E9576eE803B4eF98abe'),
        ...getAddress(mantle, 'ledger://0x5bEBA4D3533a963Dedb270a95ae5f7752fA0Fe22'),
        default: 0
    },
    deployerV2: { ...getAddress(mainnet, '0xdfeE8DC240c6CadC2c7f7f9c257c259914dEa84E') },
    foundationMultisig: { ...getAddress(mainnet, '0xeBeD45Ca22fcF70AdCcAb7618C51A3Dbb06C8d83') },
    foundationMultisig2: { ...getAddress(mainnet, '0x0c333d48Af19c2b42577f3C8f4779F0347F8C819') },
    daoMultisig: { ...getAddress(mainnet, '0x7e3692a6d8c34a762079fa9057aed87be7e67cb8') },
    ...TokenNamedAccounts,
    ...TestNamedAccounts,
    ...BancorNamedAccounts
};
//# sourceMappingURL=named-accounts.js.map