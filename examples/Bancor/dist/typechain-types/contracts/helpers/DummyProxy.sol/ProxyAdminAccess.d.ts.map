{"version": 3, "file": "ProxyAdminAccess.d.ts", "sourceRoot": "", "sources": ["../../../../../typechain-types/contracts/helpers/DummyProxy.sol/ProxyAdminAccess.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACd,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,SAAS,EACT,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,gBAAgB,EAChB,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,yBAA0B,SAAQ,KAAK,CAAC,SAAS;IAChE,SAAS,EAAE;QACT,mCAAmC,EAAE,gBAAgB,CAAC;QACtD,wBAAwB,EAAE,gBAAgB,CAAC;QAC3C,iCAAiC,EAAE,gBAAgB,CAAC;QACpD,SAAS,EAAE,gBAAgB,CAAC;QAC5B,qBAAqB,EAAE,gBAAgB,CAAC;QACxC,4BAA4B,EAAE,gBAAgB,CAAC;QAC/C,0BAA0B,EAAE,gBAAgB,CAAC;QAC7C,uCAAuC,EAAE,gBAAgB,CAAC;KAC3D,CAAC;IAEF,WAAW,CACT,sBAAsB,EAClB,kBAAkB,GAClB,eAAe,GACf,wBAAwB,GACxB,OAAO,GACP,mBAAmB,GACnB,mBAAmB,GACnB,SAAS,GACT,gBAAgB,GACnB,gBAAgB,CAAC;IAEpB,kBAAkB,CAChB,gBAAgB,EAAE,kBAAkB,EACpC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GACvB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,eAAe,EACjC,MAAM,EAAE,CAAC,MAAM,CAAC,GACf,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,wBAAwB,EAC1C,MAAM,EAAE,CAAC,MAAM,CAAC,GACf,MAAM,CAAC;IACV,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAC1E,kBAAkB,CAChB,gBAAgB,EAAE,mBAAmB,EACrC,MAAM,CAAC,EAAE,SAAS,GACjB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,mBAAmB,EACrC,MAAM,EAAE,CAAC,MAAM,CAAC,GACf,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,SAAS,EAC3B,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GACvB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,gBAAgB,EAClC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,GAClC,MAAM,CAAC;IAEV,oBAAoB,CAClB,gBAAgB,EAAE,kBAAkB,EACpC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,eAAe,EACjC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,wBAAwB,EAC1C,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IACzE,oBAAoB,CAClB,gBAAgB,EAAE,mBAAmB,EACrC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,mBAAmB,EACrC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC3E,oBAAoB,CAClB,gBAAgB,EAAE,gBAAgB,EAClC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IAEV,MAAM,EAAE;QACN,uCAAuC,EAAE,aAAa,CAAC;KACxD,CAAC;IAEF,QAAQ,CAAC,sBAAsB,EAAE,sBAAsB,GAAG,aAAa,CAAC;CACzE;AAED,MAAM,WAAW,+BAA+B;IAC9C,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC;CAClB;AACD,MAAM,MAAM,yBAAyB,GAAG,UAAU,CAChD;IAAC,MAAM;IAAE,MAAM;CAAC,EAChB,+BAA+B,CAChC,CAAC;AAEF,MAAM,MAAM,+BAA+B,GACzC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;AAE9C,MAAM,WAAW,gBAAiB,SAAQ,YAAY;IACpD,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,yBAAyB,CAAC;IAErC,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAE3E,sBAAsB,CACpB,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAErB,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEpD,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,OAAO,CACL,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,cAAc,CACZ,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GAC/C,OAAO,CAAC,mBAAmB,CAAC,CAAC;KACjC,CAAC;IAEF,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEzE,sBAAsB,CACpB,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnB,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElD,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,OAAO,CACL,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,cAAc,CACZ,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GAC/C,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,UAAU,EAAE;QACV,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAEzE,sBAAsB,CACpB,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,MAAM,CAAC,CAAC;QAEnB,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAElD,iBAAiB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5D,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,OAAO,CACL,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,cAAc,CACZ,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;KAClB,CAAC;IAEF,OAAO,EAAE;QACP,uCAAuC,CACrC,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,EAC7B,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GACvB,+BAA+B,CAAC;QACnC,oBAAoB,CAClB,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,EAC7B,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GACvB,+BAA+B,CAAC;KACpC,CAAC;IAEF,WAAW,EAAE;QACX,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5E,sBAAsB,CACpB,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAErD,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,cAAc,CACZ,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GAC/C,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,aAAa,CACX,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,sBAAsB,CACpB,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEhE,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,OAAO,CACL,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,cAAc,CACZ,KAAK,EAAE,MAAM,EACb,cAAc,EAAE,MAAM,EACtB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GAC/C,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}