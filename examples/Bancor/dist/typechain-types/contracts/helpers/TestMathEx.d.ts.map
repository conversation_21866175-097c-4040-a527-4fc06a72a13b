{"version": 3, "file": "TestMathEx.d.ts", "sourceRoot": "", "sources": ["../../../../typechain-types/contracts/helpers/TestMathEx.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,aAAa,EACb,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,MAAM,cAAc,GAAG;IAAE,CAAC,EAAE,YAAY,CAAC;IAAC,CAAC,EAAE,YAAY,CAAA;CAAE,CAAC;AAElE,MAAM,MAAM,oBAAoB,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG;IAC1D,CAAC,EAAE,SAAS,CAAC;IACb,CAAC,EAAE,SAAS,CAAC;CACd,CAAC;AAEF,MAAM,WAAW,mBAAoB,SAAQ,KAAK,CAAC,SAAS;IAC1D,SAAS,EAAE;QACT,yBAAyB,EAAE,gBAAgB,CAAC;QAC5C,4BAA4B,EAAE,gBAAgB,CAAC;QAC/C,kCAAkC,EAAE,gBAAgB,CAAC;QACrD,kCAAkC,EAAE,gBAAgB,CAAC;KACtD,CAAC;IAEF,WAAW,CACT,sBAAsB,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS,GACnE,gBAAgB,CAAC;IAEpB,kBAAkB,CAChB,gBAAgB,EAAE,MAAM,EACxB,MAAM,EAAE,CAAC,cAAc,CAAC,GACvB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,WAAW,EAC7B,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,GACnC,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,SAAS,EAC3B,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,GACjD,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,SAAS,EAC3B,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,GACjD,MAAM,CAAC;IAEV,oBAAoB,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IACxE,oBAAoB,CAAC,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC7E,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC3E,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAE3E,MAAM,EAAE,EAAE,CAAC;CACZ;AAED,MAAM,WAAW,UAAW,SAAQ,YAAY;IAC9C,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,mBAAmB,CAAC;IAE/B,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,IAAI,CACF,CAAC,EAAE,cAAc,EACjB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC;QAEnC,SAAS,CACP,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAExB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAExB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;KACzB,CAAC;IAEF,IAAI,CACF,CAAC,EAAE,cAAc,EACjB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEjC,SAAS,CACP,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;IAEtB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;IAEtB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;IAEtB,UAAU,EAAE;QACV,IAAI,CACF,CAAC,EAAE,cAAc,EACjB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,SAAS,CACP,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,OAAO,EAAE,EAAE,CAAC;IAEZ,WAAW,EAAE;QACX,IAAI,CAAC,CAAC,EAAE,cAAc,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEvE,SAAS,CACP,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,IAAI,CACF,CAAC,EAAE,cAAc,EACjB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,SAAS,CACP,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,OAAO,CACL,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,YAAY,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}