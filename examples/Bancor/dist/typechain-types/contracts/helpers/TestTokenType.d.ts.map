{"version": 3, "file": "TestTokenType.d.ts", "sourceRoot": "", "sources": ["../../../../typechain-types/contracts/helpers/TestTokenType.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,sBAAuB,SAAQ,KAAK,CAAC,SAAS;IAC7D,SAAS,EAAE;QACT,4BAA4B,EAAE,gBAAgB,CAAC;QAC/C,mBAAmB,EAAE,gBAAgB,CAAC;QACtC,0BAA0B,EAAE,gBAAgB,CAAC;QAC7C,mBAAmB,EAAE,gBAAgB,CAAC;QACtC,sCAAsC,EAAE,gBAAgB,CAAC;QACzD,uCAAuC,EAAE,gBAAgB,CAAC;QAC1D,mDAAmD,EAAE,gBAAgB,CAAC;QACtE,iBAAiB,EAAE,gBAAgB,CAAC;KACrC,CAAC;IAEF,WAAW,CACT,sBAAsB,EAClB,WAAW,GACX,UAAU,GACV,SAAS,GACT,UAAU,GACV,aAAa,GACb,cAAc,GACd,kBAAkB,GAClB,QAAQ,GACX,gBAAgB,CAAC;IAEpB,kBAAkB,CAChB,gBAAgB,EAAE,WAAW,EAC7B,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GACvB,MAAM,CAAC;IACV,kBAAkB,CAAC,gBAAgB,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC3E,kBAAkB,CAChB,gBAAgB,EAAE,SAAS,EAC3B,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GACvB,MAAM,CAAC;IACV,kBAAkB,CAAC,gBAAgB,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC3E,kBAAkB,CAChB,gBAAgB,EAAE,aAAa,EAC/B,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,GACrC,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,cAAc,EAChC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,GACrC,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,kBAAkB,EACpC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,GAC7C,MAAM,CAAC;IACV,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAEzE,oBAAoB,CAAC,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC7E,oBAAoB,CAAC,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC5E,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC3E,oBAAoB,CAAC,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC5E,oBAAoB,CAClB,gBAAgB,EAAE,aAAa,EAC/B,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,cAAc,EAChC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,kBAAkB,EACpC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAE1E,MAAM,EAAE,EAAE,CAAC;CACZ;AAED,MAAM,WAAW,aAAc,SAAQ,YAAY;IACjD,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,sBAAsB,CAAC;IAElC,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAExB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEtE,OAAO,CACL,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAEtB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAEvE,WAAW,CACT,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,YAAY,CACV,KAAK,EAAE,MAAM,EACb,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;KACrE,CAAC;IAEF,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;IAEtB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEpE,OAAO,CACL,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;IAEpB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAErE,WAAW,CACT,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,YAAY,CACV,KAAK,EAAE,MAAM,EACb,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElE,UAAU,EAAE;QACV,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAEpE,OAAO,CACL,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;QAEpB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAErE,WAAW,CACT,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,YAAY,CACV,KAAK,EAAE,MAAM,EACb,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;KACnE,CAAC;IAEF,OAAO,EAAE,EAAE,CAAC;IAEZ,WAAW,EAAE;QACX,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEvE,OAAO,CACL,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEvE,WAAW,CACT,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,YAAY,CACV,KAAK,EAAE,MAAM,EACb,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;KACtE,CAAC;IAEF,mBAAmB,EAAE;QACnB,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,QAAQ,CACN,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,OAAO,CACL,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,QAAQ,CACN,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,WAAW,CACT,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,YAAY,CACV,KAAK,EAAE,MAAM,EACb,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,gBAAgB,CACd,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}