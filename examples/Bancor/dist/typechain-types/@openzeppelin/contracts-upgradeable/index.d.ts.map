{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../typechain-types/@openzeppelin/contracts-upgradeable/index.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,KAAK,MAAM,MAAM,UAAU,CAAC;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,SAAS,CAAC;AACtC,OAAO,KAAK,KAAK,QAAQ,MAAM,YAAY,CAAC;AAC5C,OAAO,KAAK,KAAK,KAAK,MAAM,SAAS,CAAC;AACtC,OAAO,KAAK,KAAK,KAAK,MAAM,SAAS,CAAC;AAEtC,YAAY,EAAE,MAAM,EAAE,CAAC;AAEvB,YAAY,EAAE,KAAK,EAAE,CAAC;AAEtB,YAAY,EAAE,QAAQ,EAAE,CAAC;AAEzB,YAAY,EAAE,KAAK,EAAE,CAAC;AAEtB,YAAY,EAAE,KAAK,EAAE,CAAC"}