{"version": 3, "file": "IERC165Upgradeable.d.ts", "sourceRoot": "", "sources": ["../../../../../../typechain-types/@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,SAAS,EACT,aAAa,EACb,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,2BAA4B,SAAQ,KAAK,CAAC,SAAS;IAClE,SAAS,EAAE;QACT,2BAA2B,EAAE,gBAAgB,CAAC;KAC/C,CAAC;IAEF,WAAW,CAAC,sBAAsB,EAAE,mBAAmB,GAAG,gBAAgB,CAAC;IAE3E,kBAAkB,CAChB,gBAAgB,EAAE,mBAAmB,EACrC,MAAM,EAAE,CAAC,SAAS,CAAC,GAClB,MAAM,CAAC;IAEV,oBAAoB,CAClB,gBAAgB,EAAE,mBAAmB,EACrC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IAEV,MAAM,EAAE,EAAE,CAAC;CACZ;AAED,MAAM,WAAW,kBAAmB,SAAQ,YAAY;IACtD,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,2BAA2B,CAAC;IAEvC,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;KACvB,CAAC;IAEF,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;IAEpB,UAAU,EAAE;QACV,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;KACrB,CAAC;IAEF,OAAO,EAAE,EAAE,CAAC;IAEZ,WAAW,EAAE;QACX,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}