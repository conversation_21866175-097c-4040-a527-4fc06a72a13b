{"version": 3, "file": "AccessControlEnumerable.d.ts", "sourceRoot": "", "sources": ["../../../../../typechain-types/@openzeppelin/contracts/access/AccessControlEnumerable.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACd,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,gCAAiC,SAAQ,KAAK,CAAC,SAAS;IACvE,SAAS,EAAE;QACT,sBAAsB,EAAE,gBAAgB,CAAC;QACzC,uBAAuB,EAAE,gBAAgB,CAAC;QAC1C,gCAAgC,EAAE,gBAAgB,CAAC;QACnD,6BAA6B,EAAE,gBAAgB,CAAC;QAChD,4BAA4B,EAAE,gBAAgB,CAAC;QAC/C,0BAA0B,EAAE,gBAAgB,CAAC;QAC7C,+BAA+B,EAAE,gBAAgB,CAAC;QAClD,6BAA6B,EAAE,gBAAgB,CAAC;QAChD,2BAA2B,EAAE,gBAAgB,CAAC;KAC/C,CAAC;IAEF,WAAW,CACT,sBAAsB,EAClB,oBAAoB,GACpB,cAAc,GACd,eAAe,GACf,oBAAoB,GACpB,WAAW,GACX,SAAS,GACT,cAAc,GACd,YAAY,GACZ,mBAAmB,GACtB,gBAAgB,CAAC;IAEpB,kBAAkB,CAChB,gBAAgB,EAAE,oBAAoB,EACtC,MAAM,CAAC,EAAE,SAAS,GACjB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,cAAc,EAChC,MAAM,EAAE,CAAC,SAAS,CAAC,GAClB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,eAAe,EACjC,MAAM,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,GAChC,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,oBAAoB,EACtC,MAAM,EAAE,CAAC,SAAS,CAAC,GAClB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,WAAW,EAC7B,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,GAC1B,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,SAAS,EAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,GAC1B,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,cAAc,EAChC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,GAC1B,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,YAAY,EAC9B,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,GAC1B,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,mBAAmB,EACrC,MAAM,EAAE,CAAC,SAAS,CAAC,GAClB,MAAM,CAAC;IAEV,oBAAoB,CAClB,gBAAgB,EAAE,oBAAoB,EACtC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,cAAc,EAChC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,eAAe,EACjC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,oBAAoB,EACtC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC7E,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC3E,oBAAoB,CAClB,gBAAgB,EAAE,cAAc,EAChC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC9E,oBAAoB,CAClB,gBAAgB,EAAE,mBAAmB,EACrC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IAEV,MAAM,EAAE;QACN,2CAA2C,EAAE,aAAa,CAAC;QAC3D,sCAAsC,EAAE,aAAa,CAAC;QACtD,sCAAsC,EAAE,aAAa,CAAC;KACvD,CAAC;IAEF,QAAQ,CAAC,sBAAsB,EAAE,kBAAkB,GAAG,aAAa,CAAC;IACpE,QAAQ,CAAC,sBAAsB,EAAE,aAAa,GAAG,aAAa,CAAC;IAC/D,QAAQ,CAAC,sBAAsB,EAAE,aAAa,GAAG,aAAa,CAAC;CAChE;AAED,MAAM,WAAW,2BAA2B;IAC1C,IAAI,EAAE,MAAM,CAAC;IACb,iBAAiB,EAAE,MAAM,CAAC;IAC1B,YAAY,EAAE,MAAM,CAAC;CACtB;AACD,MAAM,MAAM,qBAAqB,GAAG,UAAU,CAC5C;IAAC,MAAM;IAAE,MAAM;IAAE,MAAM;CAAC,EACxB,2BAA2B,CAC5B,CAAC;AAEF,MAAM,MAAM,2BAA2B,GACrC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;AAE1C,MAAM,WAAW,sBAAsB;IACrC,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;CAChB;AACD,MAAM,MAAM,gBAAgB,GAAG,UAAU,CACvC;IAAC,MAAM;IAAE,MAAM;IAAE,MAAM;CAAC,EACxB,sBAAsB,CACvB,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAExE,MAAM,WAAW,sBAAsB;IACrC,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;CAChB;AACD,MAAM,MAAM,gBAAgB,GAAG,UAAU,CACvC;IAAC,MAAM;IAAE,MAAM;IAAE,MAAM;CAAC,EACxB,sBAAsB,CACvB,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAExE,MAAM,WAAW,uBAAwB,SAAQ,YAAY;IAC3D,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,gCAAgC,CAAC;IAE5C,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,kBAAkB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEjE,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAE5E,aAAa,CACX,IAAI,EAAE,SAAS,EACf,KAAK,EAAE,YAAY,EACnB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAErB,kBAAkB,CAChB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAExB,SAAS,CACP,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,OAAO,CACL,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAEtB,YAAY,CACV,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,UAAU,CACR,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;KACvB,CAAC;IAEF,kBAAkB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE/D,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE1E,aAAa,CACX,IAAI,EAAE,SAAS,EACf,KAAK,EAAE,YAAY,EACnB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnB,kBAAkB,CAChB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;IAEtB,SAAS,CACP,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,OAAO,CACL,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;IAEpB,YAAY,CACV,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,UAAU,CACR,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;IAEpB,UAAU,EAAE;QACV,kBAAkB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE/D,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE1E,aAAa,CACX,IAAI,EAAE,SAAS,EACf,KAAK,EAAE,YAAY,EACnB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,MAAM,CAAC,CAAC;QAEnB,kBAAkB,CAChB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,SAAS,CACP,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,OAAO,CACL,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;QAEpB,YAAY,CACV,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,UAAU,CACR,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjB,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;KACrB,CAAC;IAEF,OAAO,EAAE;QACP,2CAA2C,CACzC,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,EACvB,iBAAiB,CAAC,EAAE,SAAS,GAAG,IAAI,EACpC,YAAY,CAAC,EAAE,SAAS,GAAG,IAAI,GAC9B,2BAA2B,CAAC;QAC/B,gBAAgB,CACd,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,EACvB,iBAAiB,CAAC,EAAE,SAAS,GAAG,IAAI,EACpC,YAAY,CAAC,EAAE,SAAS,GAAG,IAAI,GAC9B,2BAA2B,CAAC;QAE/B,sCAAsC,CACpC,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,EACvB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,EACvB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GACrB,sBAAsB,CAAC;QAC1B,WAAW,CACT,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,EACvB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,EACvB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GACrB,sBAAsB,CAAC;QAE1B,sCAAsC,CACpC,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,EACvB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,EACvB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GACrB,sBAAsB,CAAC;QAC1B,WAAW,CACT,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,EACvB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,EACvB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GACrB,sBAAsB,CAAC;KAC3B,CAAC;IAEF,WAAW,EAAE;QACX,kBAAkB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAElE,YAAY,CACV,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,aAAa,CACX,IAAI,EAAE,SAAS,EACf,KAAK,EAAE,YAAY,EACnB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,kBAAkB,CAChB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,SAAS,CACP,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,YAAY,CACV,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,UAAU,CACR,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,kBAAkB,CAChB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,YAAY,CACV,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,aAAa,CACX,IAAI,EAAE,SAAS,EACf,KAAK,EAAE,YAAY,EACnB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,kBAAkB,CAChB,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,SAAS,CACP,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,OAAO,CACL,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,YAAY,CACV,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,UAAU,CACR,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,iBAAiB,CACf,WAAW,EAAE,SAAS,EACtB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}