{"version": 3, "file": "Ownable.d.ts", "sourceRoot": "", "sources": ["../../../../../typechain-types/@openzeppelin/contracts/access/Ownable.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACd,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,SAAS,EACT,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,gBAAiB,SAAQ,KAAK,CAAC,SAAS;IACvD,SAAS,EAAE;QACT,SAAS,EAAE,gBAAgB,CAAC;QAC5B,qBAAqB,EAAE,gBAAgB,CAAC;QACxC,4BAA4B,EAAE,gBAAgB,CAAC;KAChD,CAAC;IAEF,WAAW,CACT,sBAAsB,EAAE,OAAO,GAAG,mBAAmB,GAAG,mBAAmB,GAC1E,gBAAgB,CAAC;IAEpB,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAC1E,kBAAkB,CAChB,gBAAgB,EAAE,mBAAmB,EACrC,MAAM,CAAC,EAAE,SAAS,GACjB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,mBAAmB,EACrC,MAAM,EAAE,CAAC,MAAM,CAAC,GACf,MAAM,CAAC;IAEV,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IACzE,oBAAoB,CAClB,gBAAgB,EAAE,mBAAmB,EACrC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,mBAAmB,EACrC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IAEV,MAAM,EAAE;QACN,uCAAuC,EAAE,aAAa,CAAC;KACxD,CAAC;IAEF,QAAQ,CAAC,sBAAsB,EAAE,sBAAsB,GAAG,aAAa,CAAC;CACzE;AAED,MAAM,WAAW,+BAA+B;IAC9C,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC;CAClB;AACD,MAAM,MAAM,yBAAyB,GAAG,UAAU,CAChD;IAAC,MAAM;IAAE,MAAM;CAAC,EAChB,+BAA+B,CAChC,CAAC;AAEF,MAAM,MAAM,+BAA+B,GACzC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;AAE9C,MAAM,WAAW,OAAQ,SAAQ,YAAY;IAC3C,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,gBAAgB,CAAC;IAE5B,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEpD,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;KACjC,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElD,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,UAAU,EAAE;QACV,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAElD,iBAAiB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5D,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;KAClB,CAAC;IAEF,OAAO,EAAE;QACP,uCAAuC,CACrC,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,EAC7B,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GACvB,+BAA+B,CAAC;QACnC,oBAAoB,CAClB,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,EAC7B,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,GACvB,+BAA+B,CAAC;KACpC,CAAC;IAEF,WAAW,EAAE;QACX,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAErD,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEhE,iBAAiB,CACf,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}