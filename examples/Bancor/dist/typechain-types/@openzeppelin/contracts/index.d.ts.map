{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../typechain-types/@openzeppelin/contracts/index.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,KAAK,MAAM,MAAM,UAAU,CAAC;AACxC,OAAO,KAAK,KAAK,UAAU,MAAM,cAAc,CAAC;AAChD,OAAO,KAAK,KAAK,KAAK,MAAM,SAAS,CAAC;AACtC,OAAO,KAAK,KAAK,KAAK,MAAM,SAAS,CAAC;AACtC,OAAO,KAAK,KAAK,KAAK,MAAM,SAAS,CAAC;AAEtC,YAAY,EAAE,MAAM,EAAE,CAAC;AAEvB,YAAY,EAAE,UAAU,EAAE,CAAC;AAE3B,YAAY,EAAE,KAAK,EAAE,CAAC;AAEtB,YAAY,EAAE,KAAK,EAAE,CAAC;AAEtB,YAAY,EAAE,KAAK,EAAE,CAAC"}