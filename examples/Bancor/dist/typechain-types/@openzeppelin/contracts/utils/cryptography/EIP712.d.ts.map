{"version": 3, "file": "EIP712.d.ts", "sourceRoot": "", "sources": ["../../../../../../typechain-types/@openzeppelin/contracts/utils/cryptography/EIP712.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACd,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,SAAS,EACT,aAAa,EACb,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,eAAgB,SAAQ,KAAK,CAAC,SAAS;IACtD,SAAS,EAAE;QACT,gBAAgB,EAAE,gBAAgB,CAAC;KACpC,CAAC;IAEF,WAAW,CAAC,sBAAsB,EAAE,cAAc,GAAG,gBAAgB,CAAC;IAEtE,kBAAkB,CAChB,gBAAgB,EAAE,cAAc,EAChC,MAAM,CAAC,EAAE,SAAS,GACjB,MAAM,CAAC;IAEV,oBAAoB,CAClB,gBAAgB,EAAE,cAAc,EAChC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IAEV,MAAM,EAAE;QACN,uBAAuB,EAAE,aAAa,CAAC;KACxC,CAAC;IAEF,QAAQ,CAAC,sBAAsB,EAAE,qBAAqB,GAAG,aAAa,CAAC;CACxE;AAED,MAAM,WAAW,8BAA8B;CAAG;AAClD,MAAM,MAAM,wBAAwB,GAAG,UAAU,CAC/C;CAAE,EACF,8BAA8B,CAC/B,CAAC;AAEF,MAAM,MAAM,8BAA8B,GACxC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAE7C,MAAM,WAAW,MAAO,SAAQ,YAAY;IAC1C,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,eAAe,CAAC;IAE3B,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,YAAY,CACV,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CACR;YAAC,MAAM;YAAE,MAAM;YAAE,MAAM;YAAE,SAAS;YAAE,MAAM;YAAE,MAAM;YAAE,SAAS,EAAE;SAAC,GAAG;YACjE,MAAM,EAAE,MAAM,CAAC;YACf,IAAI,EAAE,MAAM,CAAC;YACb,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,SAAS,CAAC;YACnB,iBAAiB,EAAE,MAAM,CAAC;YAC1B,IAAI,EAAE,MAAM,CAAC;YACb,UAAU,EAAE,SAAS,EAAE,CAAC;SACzB,CACF,CAAC;KACH,CAAC;IAEF,YAAY,CACV,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CACR;QAAC,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,SAAS;QAAE,MAAM;QAAE,MAAM;QAAE,SAAS,EAAE;KAAC,GAAG;QACjE,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,SAAS,CAAC;QACnB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,IAAI,EAAE,MAAM,CAAC;QACb,UAAU,EAAE,SAAS,EAAE,CAAC;KACzB,CACF,CAAC;IAEF,UAAU,EAAE;QACV,YAAY,CACV,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CACR;YAAC,MAAM;YAAE,MAAM;YAAE,MAAM;YAAE,SAAS;YAAE,MAAM;YAAE,MAAM;YAAE,SAAS,EAAE;SAAC,GAAG;YACjE,MAAM,EAAE,MAAM,CAAC;YACf,IAAI,EAAE,MAAM,CAAC;YACb,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,SAAS,CAAC;YACnB,iBAAiB,EAAE,MAAM,CAAC;YAC1B,IAAI,EAAE,MAAM,CAAC;YACb,UAAU,EAAE,SAAS,EAAE,CAAC;SACzB,CACF,CAAC;KACH,CAAC;IAEF,OAAO,EAAE;QACP,uBAAuB,IAAI,8BAA8B,CAAC;QAC1D,mBAAmB,IAAI,8BAA8B,CAAC;KACvD,CAAC;IAEF,WAAW,EAAE;QACX,YAAY,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;KAC7D,CAAC;IAEF,mBAAmB,EAAE;QACnB,YAAY,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;KACxE,CAAC;CACH"}