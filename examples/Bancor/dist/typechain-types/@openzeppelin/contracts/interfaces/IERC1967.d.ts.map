{"version": 3, "file": "IERC1967.d.ts", "sourceRoot": "", "sources": ["../../../../../typechain-types/@openzeppelin/contracts/interfaces/IERC1967.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACxD,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC;AAE1D,MAAM,WAAW,iBAAkB,SAAQ,KAAK,CAAC,SAAS;IACxD,SAAS,EAAE,EAAE,CAAC;IAEd,MAAM,EAAE;QACN,+BAA+B,EAAE,aAAa,CAAC;QAC/C,yBAAyB,EAAE,aAAa,CAAC;QACzC,mBAAmB,EAAE,aAAa,CAAC;KACpC,CAAC;IAEF,QAAQ,CAAC,sBAAsB,EAAE,cAAc,GAAG,aAAa,CAAC;IAChE,QAAQ,CAAC,sBAAsB,EAAE,gBAAgB,GAAG,aAAa,CAAC;IAClE,QAAQ,CAAC,sBAAsB,EAAE,UAAU,GAAG,aAAa,CAAC;CAC7D;AAED,MAAM,WAAW,uBAAuB;IACtC,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC;CAClB;AACD,MAAM,MAAM,iBAAiB,GAAG,UAAU,CACxC;IAAC,MAAM;IAAE,MAAM;CAAC,EAChB,uBAAuB,CACxB,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AAE1E,MAAM,WAAW,yBAAyB;IACxC,MAAM,EAAE,MAAM,CAAC;CAChB;AACD,MAAM,MAAM,mBAAmB,GAAG,UAAU,CAC1C;IAAC,MAAM;CAAC,EACR,yBAAyB,CAC1B,CAAC;AAEF,MAAM,MAAM,yBAAyB,GAAG,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;AAE9E,MAAM,WAAW,mBAAmB;IAClC,cAAc,EAAE,MAAM,CAAC;CACxB;AACD,MAAM,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,MAAM,CAAC,EAAE,mBAAmB,CAAC,CAAC;AAEtE,MAAM,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAElE,MAAM,WAAW,QAAS,SAAQ,YAAY;IAC5C,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,iBAAiB,CAAC;IAE7B,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE,EAAE,CAAC;IAEd,UAAU,EAAE,EAAE,CAAC;IAEf,OAAO,EAAE;QACP,+BAA+B,CAC7B,aAAa,CAAC,EAAE,IAAI,EACpB,QAAQ,CAAC,EAAE,IAAI,GACd,uBAAuB,CAAC;QAC3B,YAAY,CACV,aAAa,CAAC,EAAE,IAAI,EACpB,QAAQ,CAAC,EAAE,IAAI,GACd,uBAAuB,CAAC;QAE3B,yBAAyB,CACvB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GACrB,yBAAyB,CAAC;QAC7B,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,yBAAyB,CAAC;QAElE,mBAAmB,CAAC,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,mBAAmB,CAAC;QACzE,QAAQ,CAAC,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,mBAAmB,CAAC;KAC/D,CAAC;IAEF,WAAW,EAAE,EAAE,CAAC;IAEhB,mBAAmB,EAAE,EAAE,CAAC;CACzB"}