{"version": 3, "file": "IERC20Permit.d.ts", "sourceRoot": "", "sources": ["../../../../../../../typechain-types/@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,uBAAuB,CAAC;AAC/B,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,qBAAsB,SAAQ,KAAK,CAAC,SAAS;IAC5D,SAAS,EAAE;QACT,oBAAoB,EAAE,gBAAgB,CAAC;QACvC,iBAAiB,EAAE,gBAAgB,CAAC;QACpC,+DAA+D,EAAE,gBAAgB,CAAC;KACnF,CAAC;IAEF,WAAW,CACT,sBAAsB,EAAE,kBAAkB,GAAG,QAAQ,GAAG,QAAQ,GAC/D,gBAAgB,CAAC;IAEpB,kBAAkB,CAChB,gBAAgB,EAAE,kBAAkB,EACpC,MAAM,CAAC,EAAE,SAAS,GACjB,MAAM,CAAC;IACV,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IACzE,kBAAkB,CAChB,gBAAgB,EAAE,QAAQ,EAC1B,MAAM,EAAE;QACN,MAAM;QACN,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,SAAS;KACV,GACA,MAAM,CAAC;IAEV,oBAAoB,CAClB,gBAAgB,EAAE,kBAAkB,EACpC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC1E,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAE1E,MAAM,EAAE,EAAE,CAAC;CACZ;AAED,MAAM,WAAW,YAAa,SAAQ,YAAY;IAChD,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,qBAAqB,CAAC;IAEjC,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,gBAAgB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAE/D,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAEvE,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,YAAY,EACnB,QAAQ,EAAE,YAAY,EACtB,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,SAAS,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;KACjC,CAAC;IAEF,gBAAgB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE7D,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;IAErE,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,YAAY,EACnB,QAAQ,EAAE,YAAY,EACtB,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,SAAS,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,UAAU,EAAE;QACV,gBAAgB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE7D,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAErE,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,YAAY,EACnB,QAAQ,EAAE,YAAY,EACtB,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,SAAS,EACZ,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;KAClB,CAAC;IAEF,OAAO,EAAE,EAAE,CAAC;IAEZ,WAAW,EAAE;QACX,gBAAgB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEhE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAErE,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,YAAY,EACnB,QAAQ,EAAE,YAAY,EACtB,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,SAAS,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,gBAAgB,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAE3E,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,YAAY,EACnB,QAAQ,EAAE,YAAY,EACtB,CAAC,EAAE,YAAY,EACf,CAAC,EAAE,SAAS,EACZ,CAAC,EAAE,SAAS,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}