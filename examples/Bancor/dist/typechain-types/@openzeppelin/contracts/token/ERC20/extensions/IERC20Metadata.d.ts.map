{"version": 3, "file": "IERC20Metadata.d.ts", "sourceRoot": "", "sources": ["../../../../../../../typechain-types/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,uBAAuB,CAAC;AAC/B,OAAO,KAAK,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACd,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,uBAAwB,SAAQ,KAAK,CAAC,SAAS;IAC9D,SAAS,EAAE;QACT,4BAA4B,EAAE,gBAAgB,CAAC;QAC/C,0BAA0B,EAAE,gBAAgB,CAAC;QAC7C,oBAAoB,EAAE,gBAAgB,CAAC;QACvC,YAAY,EAAE,gBAAgB,CAAC;QAC/B,QAAQ,EAAE,gBAAgB,CAAC;QAC3B,UAAU,EAAE,gBAAgB,CAAC;QAC7B,eAAe,EAAE,gBAAgB,CAAC;QAClC,2BAA2B,EAAE,gBAAgB,CAAC;QAC9C,uCAAuC,EAAE,gBAAgB,CAAC;KAC3D,CAAC;IAEF,WAAW,CACT,sBAAsB,EAClB,WAAW,GACX,SAAS,GACT,WAAW,GACX,UAAU,GACV,MAAM,GACN,QAAQ,GACR,aAAa,GACb,UAAU,GACV,cAAc,GACjB,gBAAgB,CAAC;IAEpB,kBAAkB,CAChB,gBAAgB,EAAE,WAAW,EAC7B,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GACvB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,SAAS,EAC3B,MAAM,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC,GAC7B,MAAM,CAAC;IACV,kBAAkB,CAAC,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC5E,kBAAkB,CAAC,gBAAgB,EAAE,UAAU,EAAE,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAC7E,kBAAkB,CAAC,gBAAgB,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IACzE,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAC3E,kBAAkB,CAChB,gBAAgB,EAAE,aAAa,EAC/B,MAAM,CAAC,EAAE,SAAS,GACjB,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,UAAU,EAC5B,MAAM,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC,GAC7B,MAAM,CAAC;IACV,kBAAkB,CAChB,gBAAgB,EAAE,cAAc,EAChC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,GACrC,MAAM,CAAC;IAEV,oBAAoB,CAAC,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC7E,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC3E,oBAAoB,CAAC,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC7E,oBAAoB,CAAC,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC5E,oBAAoB,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IACxE,oBAAoB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC1E,oBAAoB,CAClB,gBAAgB,EAAE,aAAa,EAC/B,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC5E,oBAAoB,CAClB,gBAAgB,EAAE,cAAc,EAChC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IAEV,MAAM,EAAE;QACN,mCAAmC,EAAE,aAAa,CAAC;QACnD,mCAAmC,EAAE,aAAa,CAAC;KACpD,CAAC;IAEF,QAAQ,CAAC,sBAAsB,EAAE,UAAU,GAAG,aAAa,CAAC;IAC5D,QAAQ,CAAC,sBAAsB,EAAE,UAAU,GAAG,aAAa,CAAC;CAC7D;AAED,MAAM,WAAW,mBAAmB;IAClC,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,SAAS,CAAC;CAClB;AACD,MAAM,MAAM,aAAa,GAAG,UAAU,CACpC;IAAC,MAAM;IAAE,MAAM;IAAE,SAAS;CAAC,EAC3B,mBAAmB,CACpB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAElE,MAAM,WAAW,mBAAmB;IAClC,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,SAAS,CAAC;CAClB;AACD,MAAM,MAAM,aAAa,GAAG,UAAU,CACpC;IAAC,MAAM;IAAE,MAAM;IAAE,SAAS;CAAC,EAC3B,mBAAmB,CACpB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAElE,MAAM,WAAW,cAAe,SAAQ,YAAY;IAClD,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,uBAAuB,CAAC;IAEnC,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAExB,OAAO,CACL,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAE5E,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEvD,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEnD,MAAM,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAErD,WAAW,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QAE7D,QAAQ,CACN,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;KACjC,CAAC;IAEF,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;IAEtB,OAAO,CACL,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;IAE1E,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAErD,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEjD,MAAM,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnD,WAAW,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;IAE3D,QAAQ,CACN,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,UAAU,EAAE;QACV,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;QAEpB,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE1E,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAErD,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAEjD,MAAM,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAEnD,WAAW,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE3D,QAAQ,CACN,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;QAEpB,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,OAAO,CAAC,CAAC;KACrB,CAAC;IAEF,OAAO,EAAE;QACP,mCAAmC,CACjC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,EACrB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,EACvB,KAAK,CAAC,EAAE,IAAI,GACX,mBAAmB,CAAC;QACvB,QAAQ,CACN,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,EACrB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,EACvB,KAAK,CAAC,EAAE,IAAI,GACX,mBAAmB,CAAC;QAEvB,mCAAmC,CACjC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,EACpB,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,EAClB,KAAK,CAAC,EAAE,IAAI,GACX,mBAAmB,CAAC;QACvB,QAAQ,CACN,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,EACpB,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,EAClB,KAAK,CAAC,EAAE,IAAI,GACX,mBAAmB,CAAC;KACxB,CAAC;IAEF,WAAW,EAAE;QACX,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,OAAO,CACL,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE1E,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAExD,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEpD,MAAM,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtD,WAAW,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE3D,QAAQ,CACN,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,SAAS,CACP,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,OAAO,CACL,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,SAAS,CACP,OAAO,EAAE,MAAM,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,QAAQ,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEnE,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAE/D,MAAM,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjE,WAAW,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEtE,QAAQ,CACN,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,YAAY,EACpB,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}