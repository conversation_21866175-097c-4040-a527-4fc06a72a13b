{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../../typechain-types/@openzeppelin/contracts/proxy/index.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,KAAK,OAAO,MAAM,WAAW,CAAC;AAC1C,OAAO,KAAK,KAAK,MAAM,MAAM,UAAU,CAAC;AACxC,OAAO,KAAK,KAAK,WAAW,MAAM,eAAe,CAAC;AAElD,YAAY,EAAE,OAAO,EAAE,CAAC;AAExB,YAAY,EAAE,MAAM,EAAE,CAAC;AAEvB,YAAY,EAAE,WAAW,EAAE,CAAC;AAC5B,YAAY,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC"}