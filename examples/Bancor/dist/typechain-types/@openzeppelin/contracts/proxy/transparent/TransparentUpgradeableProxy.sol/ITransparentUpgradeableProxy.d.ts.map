{"version": 3, "file": "ITransparentUpgradeableProxy.d.ts", "sourceRoot": "", "sources": ["../../../../../../../typechain-types/@openzeppelin/contracts/proxy/transparent/TransparentUpgradeableProxy.sol/ITransparentUpgradeableProxy.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACR,MAAM,uBAAuB,CAAC;AAC/B,OAAO,KAAK,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACd,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,SAAS,EACT,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,gBAAgB,EAChB,oBAAoB,EACpB,MAAM,EACN,KAAK,EACN,MAAM,QAAQ,CAAC;AAEhB,MAAM,WAAW,qCAAsC,SAAQ,KAAK,CAAC,SAAS;IAC5E,SAAS,EAAE;QACT,SAAS,EAAE,gBAAgB,CAAC;QAC5B,sBAAsB,EAAE,gBAAgB,CAAC;QACzC,kBAAkB,EAAE,gBAAgB,CAAC;QACrC,oBAAoB,EAAE,gBAAgB,CAAC;QACvC,iCAAiC,EAAE,gBAAgB,CAAC;KACrD,CAAC;IAEF,WAAW,CACT,sBAAsB,EAClB,OAAO,GACP,aAAa,GACb,gBAAgB,GAChB,WAAW,GACX,kBAAkB,GACrB,gBAAgB,CAAC;IAEpB,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAC1E,kBAAkB,CAAC,gBAAgB,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC9E,kBAAkB,CAChB,gBAAgB,EAAE,gBAAgB,EAClC,MAAM,CAAC,EAAE,SAAS,GACjB,MAAM,CAAC;IACV,kBAAkB,CAAC,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC5E,kBAAkB,CAChB,gBAAgB,EAAE,kBAAkB,EACpC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,GAC1B,MAAM,CAAC;IAEV,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IACzE,oBAAoB,CAClB,gBAAgB,EAAE,aAAa,EAC/B,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAClB,gBAAgB,EAAE,gBAAgB,EAClC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IACV,oBAAoB,CAAC,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC;IAC7E,oBAAoB,CAClB,gBAAgB,EAAE,kBAAkB,EACpC,IAAI,EAAE,SAAS,GACd,MAAM,CAAC;IAEV,MAAM,EAAE;QACN,+BAA+B,EAAE,aAAa,CAAC;QAC/C,yBAAyB,EAAE,aAAa,CAAC;QACzC,mBAAmB,EAAE,aAAa,CAAC;KACpC,CAAC;IAEF,QAAQ,CAAC,sBAAsB,EAAE,cAAc,GAAG,aAAa,CAAC;IAChE,QAAQ,CAAC,sBAAsB,EAAE,gBAAgB,GAAG,aAAa,CAAC;IAClE,QAAQ,CAAC,sBAAsB,EAAE,UAAU,GAAG,aAAa,CAAC;CAC7D;AAED,MAAM,WAAW,uBAAuB;IACtC,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC;CAClB;AACD,MAAM,MAAM,iBAAiB,GAAG,UAAU,CACxC;IAAC,MAAM;IAAE,MAAM;CAAC,EAChB,uBAAuB,CACxB,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AAE1E,MAAM,WAAW,yBAAyB;IACxC,MAAM,EAAE,MAAM,CAAC;CAChB;AACD,MAAM,MAAM,mBAAmB,GAAG,UAAU,CAC1C;IAAC,MAAM;CAAC,EACR,yBAAyB,CAC1B,CAAC;AAEF,MAAM,MAAM,yBAAyB,GAAG,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;AAE9E,MAAM,WAAW,mBAAmB;IAClC,cAAc,EAAE,MAAM,CAAC;CACxB;AACD,MAAM,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,MAAM,CAAC,EAAE,mBAAmB,CAAC,CAAC;AAEtE,MAAM,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAElE,MAAM,WAAW,4BAA6B,SAAQ,YAAY;IAChE,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAC5D,MAAM,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1B,SAAS,EAAE,qCAAqC,CAAC;IAEjD,WAAW,CAAC,MAAM,SAAS,UAAU,EACnC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,EAC/B,oBAAoB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,EAClD,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,GACpC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1B,SAAS,CAAC,MAAM,SAAS,UAAU,EACjC,WAAW,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAChC,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,kBAAkB,CAAC,MAAM,SAAS,UAAU,EAC1C,WAAW,EAAE,gBAAgB,CAAC,MAAM,CAAC,GACpC,IAAI,CAAC;IACR,kBAAkB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7C,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACnB,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B,SAAS,EAAE;QACT,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAEpD,WAAW,CACT,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,cAAc,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAE7D,SAAS,CACP,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEhC,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GAC/C,OAAO,CAAC,mBAAmB,CAAC,CAAC;KACjC,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElD,WAAW,CACT,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,cAAc,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE3D,SAAS,CACP,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GACxC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GAC/C,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAEhC,UAAU,EAAE;QACV,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAElD,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpE,cAAc,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE3D,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAElE,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,aAAa,GACxB,OAAO,CAAC,IAAI,CAAC,CAAC;KAClB,CAAC;IAEF,OAAO,EAAE;QACP,+BAA+B,CAC7B,aAAa,CAAC,EAAE,IAAI,EACpB,QAAQ,CAAC,EAAE,IAAI,GACd,uBAAuB,CAAC;QAC3B,YAAY,CACV,aAAa,CAAC,EAAE,IAAI,EACpB,QAAQ,CAAC,EAAE,IAAI,GACd,uBAAuB,CAAC;QAE3B,yBAAyB,CACvB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GACrB,yBAAyB,CAAC;QAC7B,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,yBAAyB,CAAC;QAElE,mBAAmB,CAAC,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,mBAAmB,CAAC;QACzE,QAAQ,CAAC,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,mBAAmB,CAAC;KAC/D,CAAC;IAEF,WAAW,EAAE;QACX,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAErD,WAAW,CACT,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,cAAc,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAE9D,SAAS,CACP,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEtB,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GAC/C,OAAO,CAAC,SAAS,CAAC,CAAC;KACvB,CAAC;IAEF,mBAAmB,EAAE;QACnB,KAAK,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEhE,WAAW,CACT,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,cAAc,CAAC,SAAS,CAAC,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEzE,SAAS,CACP,IAAI,EAAE,MAAM,EACZ,SAAS,CAAC,EAAE,SAAS,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GACxC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEjC,gBAAgB,CACd,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,SAAS,EACf,SAAS,CAAC,EAAE,gBAAgB,GAAG;YAAE,IAAI,CAAC,EAAE,MAAM,CAAA;SAAE,GAC/C,OAAO,CAAC,oBAAoB,CAAC,CAAC;KAClC,CAAC;CACH"}