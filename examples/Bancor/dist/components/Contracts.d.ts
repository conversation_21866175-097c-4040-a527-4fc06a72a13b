import { CarbonController__factory, CarbonPOL__factory, CarbonVortex__factory, ERC20__factory, MockBancorNetworkV3__factory, OptimizedTransparentUpgradeableProxy__factory, ProxyAdmin__factory, TestBNT__factory, TestCarbonController__factory, TestERC20Burnable__factory, TestERC20FeeOnTransfer__factory, TestERC20Token__factory, TestExpDecayMath__factory, TestLogic__factory, TestMathEx__factory, TestOnlyProxyDelegate__factory, TestPairs__factory, TestStrategies__factory, TestTokenType__factory, TestUpgradeable__factory, TestVoucher__factory, Voucher__factory } from '../typechain-types';
import { Signer } from 'ethers';
export * from '../typechain-types';
declare const getContracts: (signer?: Signer) => {
    connect: (signer: Signer) => any;
    ERC20: import("./ContractBuilder").ContractBuilder<ERC20__factory>;
    CarbonController: import("./ContractBuilder").ContractBuilder<CarbonController__factory>;
    CarbonVortex: import("./ContractBuilder").ContractBuilder<CarbonVortex__factory>;
    CarbonPOL: import("./ContractBuilder").ContractBuilder<CarbonPOL__factory>;
    MockBancorNetworkV3: import("./ContractBuilder").ContractBuilder<MockBancorNetworkV3__factory>;
    ProxyAdmin: import("./ContractBuilder").ContractBuilder<ProxyAdmin__factory>;
    Voucher: import("./ContractBuilder").ContractBuilder<Voucher__factory>;
    TestERC20FeeOnTransfer: import("./ContractBuilder").ContractBuilder<TestERC20FeeOnTransfer__factory>;
    TestERC20Burnable: import("./ContractBuilder").ContractBuilder<TestERC20Burnable__factory>;
    TestERC20Token: import("./ContractBuilder").ContractBuilder<TestERC20Token__factory>;
    TestExpDecayMath: import("./ContractBuilder").ContractBuilder<TestExpDecayMath__factory>;
    TestBNT: import("./ContractBuilder").ContractBuilder<TestBNT__factory>;
    TestLogic: import("./ContractBuilder").ContractBuilder<TestLogic__factory>;
    TestMathEx: import("./ContractBuilder").ContractBuilder<TestMathEx__factory>;
    TestStrategies: import("./ContractBuilder").ContractBuilder<TestStrategies__factory>;
    TestPairs: import("./ContractBuilder").ContractBuilder<TestPairs__factory>;
    TestTokenType: import("./ContractBuilder").ContractBuilder<TestTokenType__factory>;
    TestUpgradeable: import("./ContractBuilder").ContractBuilder<TestUpgradeable__factory>;
    TestOnlyProxyDelegate: import("./ContractBuilder").ContractBuilder<TestOnlyProxyDelegate__factory>;
    TestVoucher: import("./ContractBuilder").ContractBuilder<TestVoucher__factory>;
    TestCarbonController: import("./ContractBuilder").ContractBuilder<TestCarbonController__factory>;
    OptimizedTransparentUpgradeableProxy: import("./ContractBuilder").ContractBuilder<OptimizedTransparentUpgradeableProxy__factory>;
};
export type ContractsType = ReturnType<typeof getContracts>;
declare const _default: {
    connect: (signer: Signer) => any;
    ERC20: import("./ContractBuilder").ContractBuilder<ERC20__factory>;
    CarbonController: import("./ContractBuilder").ContractBuilder<CarbonController__factory>;
    CarbonVortex: import("./ContractBuilder").ContractBuilder<CarbonVortex__factory>;
    CarbonPOL: import("./ContractBuilder").ContractBuilder<CarbonPOL__factory>;
    MockBancorNetworkV3: import("./ContractBuilder").ContractBuilder<MockBancorNetworkV3__factory>;
    ProxyAdmin: import("./ContractBuilder").ContractBuilder<ProxyAdmin__factory>;
    Voucher: import("./ContractBuilder").ContractBuilder<Voucher__factory>;
    TestERC20FeeOnTransfer: import("./ContractBuilder").ContractBuilder<TestERC20FeeOnTransfer__factory>;
    TestERC20Burnable: import("./ContractBuilder").ContractBuilder<TestERC20Burnable__factory>;
    TestERC20Token: import("./ContractBuilder").ContractBuilder<TestERC20Token__factory>;
    TestExpDecayMath: import("./ContractBuilder").ContractBuilder<TestExpDecayMath__factory>;
    TestBNT: import("./ContractBuilder").ContractBuilder<TestBNT__factory>;
    TestLogic: import("./ContractBuilder").ContractBuilder<TestLogic__factory>;
    TestMathEx: import("./ContractBuilder").ContractBuilder<TestMathEx__factory>;
    TestStrategies: import("./ContractBuilder").ContractBuilder<TestStrategies__factory>;
    TestPairs: import("./ContractBuilder").ContractBuilder<TestPairs__factory>;
    TestTokenType: import("./ContractBuilder").ContractBuilder<TestTokenType__factory>;
    TestUpgradeable: import("./ContractBuilder").ContractBuilder<TestUpgradeable__factory>;
    TestOnlyProxyDelegate: import("./ContractBuilder").ContractBuilder<TestOnlyProxyDelegate__factory>;
    TestVoucher: import("./ContractBuilder").ContractBuilder<TestVoucher__factory>;
    TestCarbonController: import("./ContractBuilder").ContractBuilder<TestCarbonController__factory>;
    OptimizedTransparentUpgradeableProxy: import("./ContractBuilder").ContractBuilder<OptimizedTransparentUpgradeableProxy__factory>;
};
export default _default;
//# sourceMappingURL=Contracts.d.ts.map