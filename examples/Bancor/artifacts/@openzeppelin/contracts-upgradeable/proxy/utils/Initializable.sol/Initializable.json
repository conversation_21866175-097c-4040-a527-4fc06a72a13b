{"_format": "hh-sol-artifact-1", "contractName": "Initializable", "sourceName": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}