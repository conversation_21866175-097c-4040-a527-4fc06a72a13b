{"_format": "hh-sol-artifact-1", "contractName": "IVault", "sourceName": "contracts/utility/interfaces/IVault.sol", "abi": [{"inputs": [{"internalType": "Token", "name": "token", "type": "address"}, {"internalType": "address payable", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawFunds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}