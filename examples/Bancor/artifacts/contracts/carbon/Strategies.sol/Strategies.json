{"_format": "hh-sol-artifact-1", "contractName": "Strategies", "sourceName": "contracts/carbon/Strategies.sol", "abi": [{"inputs": [], "name": "BalanceMismatch", "type": "error"}, {"inputs": [], "name": "GreaterThanMaxInput", "type": "error"}, {"inputs": [], "name": "InsufficientCapacity", "type": "error"}, {"inputs": [], "name": "InsufficientLiquidity", "type": "error"}, {"inputs": [], "name": "InvalidRate", "type": "error"}, {"inputs": [], "name": "InvalidTradeActionAmount", "type": "error"}, {"inputs": [], "name": "InvalidTradeActionStrategyId", "type": "error"}, {"inputs": [], "name": "LowerThanMinReturn", "type": "error"}, {"inputs": [], "name": "NativeAmountMismatch", "type": "error"}, {"inputs": [], "name": "OrderDisabled", "type": "error"}, {"inputs": [], "name": "OutDated", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "Token", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}], "name": "FeesWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "prevFeePPM", "type": "uint32"}, {"indexed": false, "internalType": "uint32", "name": "newFeePPM", "type": "uint32"}], "name": "PairTradingFeePPMUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order0", "type": "tuple"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order1", "type": "tuple"}], "name": "StrategyCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order0", "type": "tuple"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order1", "type": "tuple"}], "name": "StrategyDeleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order0", "type": "tuple"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order1", "type": "tuple"}, {"indexed": false, "internalType": "uint8", "name": "reason", "type": "uint8"}], "name": "StrategyUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "trader", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "sourceToken", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "targetToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "sourceAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "targetAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint128", "name": "tradingFeeAmount", "type": "uint128"}, {"indexed": false, "internalType": "bool", "name": "byTargetAmount", "type": "bool"}], "name": "TokensTraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "prevFeePPM", "type": "uint32"}, {"indexed": false, "internalType": "uint32", "name": "newFeePPM", "type": "uint32"}], "name": "TradingFeePPMUpdated", "type": "event"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}