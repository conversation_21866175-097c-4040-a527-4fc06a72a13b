{"_format": "hh-sol-artifact-1", "contractName": "MockBancorNetworkV3", "sourceName": "contracts/helpers/MockBancorNetworkV3.sol", "abi": [{"inputs": [{"internalType": "address", "name": "bnt", "type": "address"}, {"internalType": "uint256", "name": "outputAmount", "type": "uint256"}, {"internalType": "bool", "name": "profit", "type": "bool"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "InsufficientFlashLoanReturn", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "ZeroValue", "type": "error"}, {"inputs": [{"internalType": "Token", "name": "", "type": "address"}], "name": "collectionByPool", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token", "type": "address"}], "name": "resetCollectionByPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token", "type": "address"}], "name": "setCollectionByPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "sourceToken", "type": "address"}, {"internalType": "Token", "name": "targetToken", "type": "address"}, {"internalType": "uint256", "name": "sourceAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minReturnAmount", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "tradeBySourceAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}