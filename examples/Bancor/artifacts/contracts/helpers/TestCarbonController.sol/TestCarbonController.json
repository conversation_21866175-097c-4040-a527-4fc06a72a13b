{"_format": "hh-sol-artifact-1", "contractName": "TestCarbonController", "sourceName": "contracts/helpers/TestCarbonController.sol", "abi": [{"inputs": [{"internalType": "contract IVoucher", "name": "initVoucher", "type": "address"}, {"internalType": "address", "name": "proxy", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessDenied", "type": "error"}, {"inputs": [], "name": "AlreadyInitialized", "type": "error"}, {"inputs": [], "name": "BalanceMismatch", "type": "error"}, {"inputs": [], "name": "DeadlineExpired", "type": "error"}, {"inputs": [], "name": "GreaterThanMaxInput", "type": "error"}, {"inputs": [], "name": "IdenticalAddresses", "type": "error"}, {"inputs": [], "name": "InsufficientCapacity", "type": "error"}, {"inputs": [], "name": "InsufficientLiquidity", "type": "error"}, {"inputs": [], "name": "InsufficientNativeTokenReceived", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidFee", "type": "error"}, {"inputs": [], "name": "InvalidIndices", "type": "error"}, {"inputs": [], "name": "InvalidRate", "type": "error"}, {"inputs": [], "name": "InvalidTradeActionAmount", "type": "error"}, {"inputs": [], "name": "InvalidTradeActionStrategyId", "type": "error"}, {"inputs": [], "name": "LowerThanMinReturn", "type": "error"}, {"inputs": [], "name": "NativeAmountMismatch", "type": "error"}, {"inputs": [], "name": "OrderDisabled", "type": "error"}, {"inputs": [], "name": "OutDated", "type": "error"}, {"inputs": [], "name": "Overflow", "type": "error"}, {"inputs": [], "name": "PairAlreadyExists", "type": "error"}, {"inputs": [], "name": "PairDoesNotExist", "type": "error"}, {"inputs": [], "name": "Unknown<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "UnnecessaryNativeTokenReceived", "type": "error"}, {"inputs": [], "name": "ZeroValue", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "Token", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "sender", "type": "address"}], "name": "FeesWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint128", "name": "pairId", "type": "uint128"}, {"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}], "name": "PairCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "prevFeePPM", "type": "uint32"}, {"indexed": false, "internalType": "uint32", "name": "newFeePPM", "type": "uint32"}], "name": "PairTradingFeePPMUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order0", "type": "tuple"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order1", "type": "tuple"}], "name": "StrategyCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order0", "type": "tuple"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order1", "type": "tuple"}], "name": "StrategyDeleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "Token", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "token1", "type": "address"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order0", "type": "tuple"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "indexed": false, "internalType": "struct Order", "name": "order1", "type": "tuple"}, {"indexed": false, "internalType": "uint8", "name": "reason", "type": "uint8"}], "name": "StrategyUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "trader", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "sourceToken", "type": "address"}, {"indexed": true, "internalType": "Token", "name": "targetToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "sourceAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "targetAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint128", "name": "tradingFeeAmount", "type": "uint128"}, {"indexed": false, "internalType": "bool", "name": "byTargetAmount", "type": "bool"}], "name": "TokensTraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "prevFeePPM", "type": "uint32"}, {"indexed": false, "internalType": "uint32", "name": "newFeePPM", "type": "uint32"}], "name": "TradingFeePPMUpdated", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token", "type": "address"}], "name": "accumulatedFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "sourceToken", "type": "address"}, {"internalType": "Token", "name": "targetToken", "type": "address"}, {"components": [{"internalType": "uint256", "name": "strategyId", "type": "uint256"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}], "internalType": "struct TradeAction[]", "name": "tradeActions", "type": "tuple[]"}], "name": "calculateTradeSourceAmount", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "sourceToken", "type": "address"}, {"internalType": "Token", "name": "targetToken", "type": "address"}, {"components": [{"internalType": "uint256", "name": "strategyId", "type": "uint256"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}], "internalType": "struct TradeAction[]", "name": "tradeActions", "type": "tuple[]"}], "name": "calculateTradeTargetAmount", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "controllerType", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token0", "type": "address"}, {"internalType": "Token", "name": "token1", "type": "address"}], "name": "createPair", "outputs": [{"components": [{"internalType": "uint128", "name": "id", "type": "uint128"}, {"internalType": "To<PERSON>[2]", "name": "tokens", "type": "address[2]"}], "internalType": "struct Pair", "name": "", "type": "tuple"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token0", "type": "address"}, {"internalType": "Token", "name": "token1", "type": "address"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "internalType": "struct Order[2]", "name": "orders", "type": "tuple[2]"}], "name": "createStrategy", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "strategyId", "type": "uint256"}], "name": "deleteStrategy", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token0", "type": "address"}, {"internalType": "Token", "name": "token1", "type": "address"}], "name": "pair", "outputs": [{"components": [{"internalType": "uint128", "name": "id", "type": "uint128"}, {"internalType": "To<PERSON>[2]", "name": "tokens", "type": "address[2]"}], "internalType": "struct Pair", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token0", "type": "address"}, {"internalType": "Token", "name": "token1", "type": "address"}], "name": "pairTradingFeePPM", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pairs", "outputs": [{"internalType": "Token[2][]", "name": "", "type": "address[2][]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "postUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token0", "type": "address"}, {"internalType": "Token", "name": "token1", "type": "address"}, {"internalType": "uint32", "name": "newPairTradingFeePPM", "type": "uint32"}], "name": "setPairTradingFeePPM", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "newTradingFeePPM", "type": "uint32"}], "name": "setTradingFeePPM", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token0", "type": "address"}, {"internalType": "Token", "name": "token1", "type": "address"}, {"internalType": "uint256", "name": "startIndex", "type": "uint256"}, {"internalType": "uint256", "name": "endIndex", "type": "uint256"}], "name": "strategiesByPair", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "To<PERSON>[2]", "name": "tokens", "type": "address[2]"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "internalType": "struct Order[2]", "name": "orders", "type": "tuple[2]"}], "internalType": "struct Strategy[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token0", "type": "address"}, {"internalType": "Token", "name": "token1", "type": "address"}], "name": "strategiesByPairCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "strategy", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "To<PERSON>[2]", "name": "tokens", "type": "address[2]"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "internalType": "struct Order[2]", "name": "orders", "type": "tuple[2]"}], "internalType": "struct Strategy", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token", "type": "address"}], "name": "testAccumulatedFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "testSetAccumulatedFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "sourceToken", "type": "address"}, {"internalType": "Token", "name": "targetToken", "type": "address"}, {"components": [{"internalType": "uint256", "name": "strategyId", "type": "uint256"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}], "internalType": "struct TradeAction[]", "name": "tradeActions", "type": "tuple[]"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint128", "name": "minReturn", "type": "uint128"}], "name": "tradeBySourceAmount", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "sourceToken", "type": "address"}, {"internalType": "Token", "name": "targetToken", "type": "address"}, {"components": [{"internalType": "uint256", "name": "strategyId", "type": "uint256"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}], "internalType": "struct TradeAction[]", "name": "tradeActions", "type": "tuple[]"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint128", "name": "maxInput", "type": "uint128"}], "name": "tradeByTargetAmount", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "tradingFeePPM", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "strategyId", "type": "uint256"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "internalType": "struct Order[2]", "name": "currentOrders", "type": "tuple[2]"}, {"components": [{"internalType": "uint128", "name": "y", "type": "uint128"}, {"internalType": "uint128", "name": "z", "type": "uint128"}, {"internalType": "uint64", "name": "A", "type": "uint64"}, {"internalType": "uint64", "name": "B", "type": "uint64"}], "internalType": "struct Order[2]", "name": "newOrders", "type": "tuple[2]"}], "name": "updateStrategy", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "Token", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "recipient", "type": "address"}], "name": "withdrawFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}