{"_format": "hh-sol-artifact-1", "contractName": "TestOnlyProxyDelegate", "sourceName": "contracts/helpers/TestOnlyProxyDelegate.sol", "abi": [{"inputs": [{"internalType": "address", "name": "delegator", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "Unknown<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "testOnlyProxyDelegate", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x60a060405234801561001057600080fd5b5060405161015a38038061015a83398101604081905261002f91610040565b6001600160a01b0316608052610070565b60006020828403121561005257600080fd5b81516001600160a01b038116811461006957600080fd5b9392505050565b60805160d26100886000396000606d015260d26000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c8063c4cb3d0c14602d575b600080fd5b60336047565b604051901515815260200160405180910390f35b6000604f6055565b50600190565b3073ffffffffffffffffffffffffffffffffffffffff7f0000000000000000000000000000000000000000000000000000000000000000161460c3576040517fd0c8bfe500000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b56fea164736f6c6343000813000a", "deployedBytecode": "0x6080604052348015600f57600080fd5b506004361060285760003560e01c8063c4cb3d0c14602d575b600080fd5b60336047565b604051901515815260200160405180910390f35b6000604f6055565b50600190565b3073ffffffffffffffffffffffffffffffffffffffff7f0000000000000000000000000000000000000000000000000000000000000000161460c3576040517fd0c8bfe500000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b56fea164736f6c6343000813000a", "linkReferences": {}, "deployedLinkReferences": {}}