{"_format": "hh-sol-artifact-1", "contractName": "IBeacon", "sourceName": "hardhat-deploy/solc_0.8/openzeppelin/proxy/beacon/IBeacon.sol", "abi": [{"inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}